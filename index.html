<html lang="fr"><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><PERSON><PERSON><PERSON>agrance - Accueil</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&amp;family=Playfair+Display:wght@700&amp;display=swap" rel="stylesheet">
  <style>
    :root {
      --primary: #ff6b6b;
      --secondary: #23242a;
      --card-bg: rgba(40, 44, 52, 0.92);
      --glass-blur: 12px;
      --shadow: 0 8px 32px 0 rgba(0,0,0,0.35);
      --radius: 20px;
      --transition: 0.5s cubic-bezier(.4,2,.6,1);
      --text-main: #f7f7fa;
      --text-secondary: #bdbdbd;
      --header-bg: rgba(30, 32, 38, 0.95);
      --input-bg: rgba(50, 52, 60, 0.95);
      --input-border: #444;
      --input-focus: #ff6b6b;
      --card-border: rgba(255,255,255,0.07);
      --cart-bg: #ff6b6b;
      --cart-hover: #c0392b;
      --icon-bg: rgba(30,32,38,0.85);
      --icon-hover: #ff6b6b;
    }
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
      font-family: 'Poppins', sans-serif;
      background: linear-gradient(120deg, #18181c 0%, #23242a 100%);
      color: var(--text-main);
      min-height: 100vh;
      letter-spacing: 0.01em;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .centered-logo-section {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 32px;
      margin-bottom: 16px;
      animation: fadeInDown 1s;
    }
    .centered-logo-section img {
      width: 120px;
      height: auto;
      border-radius: 18px;
      box-shadow: 0 4px 16px rgba(255,255,255,0.08);
      margin-bottom: 12px;
    }
    .centered-logo-section h1 {
      font-family: 'Playfair Display', serif;
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-main);
      letter-spacing: 0.04em;
      background: linear-gradient(90deg, var(--primary) 10%, #fff 90%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 0;
      text-align: center;
    }
    @keyframes fadeInDown {
      from { opacity: 0; transform: translateY(-30px);}
      to { opacity: 1; transform: translateY(0);}
    }

    .gallery-section {
      max-width: 1200px;
      margin: 0 auto 40px auto;
      padding: 0 16px;
      width: 100%;
    }
    .gallery-title {
      font-size: 2rem;
      color: var(--primary);
      margin: 24px 0 18px 0;
      font-family: 'Playfair Display', serif;
      letter-spacing: 0.02em;
      text-align: left;
    }
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 32px;
      width: 100%;
    }
    .product-card {
      background: var(--card-bg);
      border-radius: 16px;
      box-shadow: var(--shadow);
      padding: 18px 14px 18px 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: transform 0.25s, box-shadow 0.25s;
      border: 1.5px solid var(--card-border);
      position: relative;
      min-height: 370px;
    }
    .product-card:hover {
      transform: translateY(-8px) scale(1.03);
      box-shadow: 0 8px 32px 0 rgba(255,107,107,0.18);
      border-color: var(--primary);
    }
    .product-image-wrapper {
      width: 160px;
      height: 200px;
      background: #23242a;
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 12px rgba(0,0,0,0.12);
    }
    .product-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
      transition: filter 0.3s;
      background: #23242a;
      display: block;
    }
    .product-card:hover .product-image {
      filter: brightness(1.08) saturate(1.1);
    }
    .product-title {
      font-family: 'Playfair Display', serif;
      font-size: 1.15rem;
      font-weight: 700;
      color: var(--primary);
      margin-bottom: 6px;
      text-align: center;
      min-height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .product-prices {
      font-size: 1.05rem;
      margin-bottom: 8px;
      text-align: center;
    }
    .old-price {
      text-decoration: line-through;
      margin-right: 8px;
      color: #888;
      font-weight: 400;
    }
    .new-price {
      font-weight: bold;
      color: var(--text-main);
      font-size: 1.1rem;
    }
    .product-type {
      font-size: 0.98em;
      color: var(--text-secondary);
      margin-bottom: 8px;
      text-align: center;
    }
    .product-link {
      display: inline-block;
      margin-top: 12px;
      padding: 8px 22px;
      background: var(--primary);
      color: #fff;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.05rem;
      transition: background 0.3s, transform 0.2s;
      box-shadow: 0 2px 8px rgba(255,107,107,0.13);
      letter-spacing: 0.01em;
    }
    .product-link:hover {
      background: var(--cart-hover);
      transform: scale(1.04);
    }
    footer {
      width: 100%;
      background: rgba(30,32,38,0.95);
      color: var(--text-secondary);
      text-align: center;
      padding: 18px 0 12px 0;
      font-size: 1.1rem;
      letter-spacing: 0.02em;
      margin-top: auto;
      border-top: 1px solid var(--card-border);
      box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
    }
    @media (max-width: 900px) {
      .centered-logo-section img { width: 80px; }
      .centered-logo-section h1 { font-size: 1.5rem; }
      .gallery-title { font-size: 1.3rem; }
      .gallery-grid { gap: 18px; }
      .product-card { min-height: 320px; }
      .product-image-wrapper { width: 110px; height: 140px; }
    }
    @media (max-width: 600px) {
      .centered-logo-section { margin-top: 16px; }
      .gallery-section { padding: 0 2px; }
      .gallery-title { font-size: 1.1rem; }
      .product-image-wrapper { width: 90px; height: 110px; }
      .product-card { min-height: 220px; padding: 10px 4px; }
    }
  </style>
</head>
<body>
  <div class="centered-logo-section">
    <a href="index.html">
      <img src="logo.png" alt="Luxury Perfumes Logo">
    </a>
    <h1>Mosaab fragrance</h1>
  </div>
  <div class="gallery-section">
    <div class="gallery-title">Parfums Homme</div>
    <div class="gallery-grid" id="gallery-men">
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=9pmafnan">
                <img src="img/9pmafnan/1.jpg" alt="9PM Afnan" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">9PM Afnan</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">550 MAD</span>
              <span class="new-price">470 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=9pmafnan">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=elexir">
                <img src="img/elexir/1.jpg" alt="Le Male Elixir" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Le Male Elixir</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1600 MAD</span>
              <span class="new-price">1500 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=elexir">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=erbapurra">
                <img src="img/erbapurra/1.jpg" alt="Erba Pura" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Erba Pura</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1200 MAD</span>
              <span class="new-price">1100 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=erbapurra">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=lebeauedp">
                <img src="img/lebeauedp/1.jpg" alt="Le Beau EDP" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Le Beau EDP</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1300 MAD</span>
              <span class="new-price">1200 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=lebeauedp">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=lebeauedt">
                <img src="img/lebeauedt/1.jpg" alt="Le Beau EDT" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Le Beau EDT</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1100 MAD</span>
              <span class="new-price">1000 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=lebeauedt">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=naxos">
                <img src="img/naxos/1.jpg" alt="Naxos" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Naxos</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1400 MAD</span>
              <span class="new-price">1300 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=naxos">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=ninepmrebel">
                <img src="img/ninepmrebel/1.jpg" alt="9PM Rebel" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">9PM Rebel</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">600 MAD</span>
              <span class="new-price">520 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=ninepmrebel">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=paradise">
                <img src="img/paradise/1.jpg" alt="Paradise" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Paradise</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">700 MAD</span>
              <span class="new-price">630 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=paradise">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=stronger">
                <img src="img/stronger/1.jpg" alt="Stronger" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Stronger</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">800 MAD</span>
              <span class="new-price">720 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=stronger">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=ultamale">
                <img src="img/ultamale/1.jpg" alt="Ultra Male" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Ultra Male</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">900 MAD</span>
              <span class="new-price">820 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=ultamale">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=valentino">
                <img src="img/valentino/1.jpg" alt="Valentino" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Valentino</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1000 MAD</span>
              <span class="new-price">900 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=valentino">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=versaccceeros">
                <img src="img/versaccceeros/1.jpg" alt="Versace Eros" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Versace Eros</div>
            <div class="product-type">Homme</div>
            <div class="product-prices">
              <span class="old-price">1100 MAD</span>
              <span class="new-price">1000 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=versaccceeros">Voir le parfum</a>
          </div>
        </div>
    <div class="gallery-title" style="margin-top:36px;">Parfums Femme</div>
    <div class="gallery-grid" id="gallery-women">
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=divine">
                <img src="imgg/divine/1.jpg" alt="Divine" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Divine</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1200 MAD</span>
              <span class="new-price">1100 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=divine">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=dolcegbnlightblue">
                <img src="imgg/dolcegbnlightblue/1.jpg" alt="Dolce Gabbana Light Blue" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Dolce Gabbana Light Blue</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1300 MAD</span>
              <span class="new-price">1200 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=dolcegbnlightblue">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=gabriellechanelessence">
                <img src="imgg/gabriellechanelessence/1.jpg" alt="Gabrielle Chanel Essence" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Gabrielle Chanel Essence</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1400 MAD</span>
              <span class="new-price">1300 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=gabriellechanelessence">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=jpgbelle">
                <img src="imgg/jpgbelle/1.jpg" alt="JPG Belle" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">JPG Belle</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1500 MAD</span>
              <span class="new-price">1400 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=jpgbelle">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=scandalgoldedp">
                <img src="imgg/scandalgoldedp/1.jpg" alt="Scandal Gold EDP" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">Scandal Gold EDP</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1600 MAD</span>
              <span class="new-price">1500 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=scandalgoldedp">Voir le parfum</a>
          </div>
        
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=theone">
                <img src="imgg/theone/1.jpg" alt="The One" class="product-image" loading="lazy" onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';">
              </a>
            </div>
            <div class="product-title">The One</div>
            <div class="product-type">Femme</div>
            <div class="product-prices">
              <span class="old-price">1700 MAD</span>
              <span class="new-price">1600 MAD</span>
            </div>
            <a class="product-link" href="product.html?folder=theone">Voir le parfum</a>
          </div>
        </div>
  </div>
  <footer>© 2025</footer>
  <script>
    function escapeHTML(str) {
      return String(str)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#39;");
    }
    const products = [
      // MEN
      { name: '9PM Afnan', folder: '9pmafnan', oldPrice: '550 MAD', newPrice: '470 MAD', type: 'men', description: 'A bold, long-lasting fragrance with notes of apple, cinnamon, and vanilla. Perfect for evening wear.' },
      { name: 'Le Male Elixir', folder: 'elexir', oldPrice: '1600 MAD', newPrice: '1500 MAD', type: 'men', description: 'A modern twist on a classic scent, blending lavender, mint, and warm woods for a seductive aura.' },
      { name: 'Erba Pura', folder: 'erbapurra', oldPrice: '1200 MAD', newPrice: '1100 MAD', type: 'men', description: '' },
      { name: 'Le Beau EDP', folder: 'lebeauedp', oldPrice: '1300 MAD', newPrice: '1200 MAD', type: 'men', description: '' },
      { name: 'Le Beau EDT', folder: 'lebeauedt', oldPrice: '1100 MAD', newPrice: '1000 MAD', type: 'men', description: '' },
      { name: 'Naxos', folder: 'naxos', oldPrice: '1400 MAD', newPrice: '1300 MAD', type: 'men', description: '' },
      { name: '9PM Rebel', folder: 'ninepmrebel', oldPrice: '600 MAD', newPrice: '520 MAD', type: 'men', description: '' },
      { name: 'Paradise', folder: 'paradise', oldPrice: '700 MAD', newPrice: '630 MAD', type: 'men', description: '' },
      { name: 'Stronger', folder: 'stronger', oldPrice: '800 MAD', newPrice: '720 MAD', type: 'men', description: '' },
      { name: 'Ultra Male', folder: 'ultamale', oldPrice: '900 MAD', newPrice: '820 MAD', type: 'men', description: '' },
      { name: 'Valentino', folder: 'valentino', oldPrice: '1000 MAD', newPrice: '900 MAD', type: 'men', description: '' },
      { name: 'Versace Eros', folder: 'versaccceeros', oldPrice: '1100 MAD', newPrice: '1000 MAD', type: 'men', description: '' },
      // WOMEN
      { name: 'Divine', folder: 'divine', oldPrice: '1200 MAD', newPrice: '1100 MAD', type: 'women', description: '' },
      { name: 'Dolce Gabbana Light Blue', folder: 'dolcegbnlightblue', oldPrice: '1300 MAD', newPrice: '1200 MAD', type: 'women', description: '' },
      { name: 'Gabrielle Chanel Essence', folder: 'gabriellechanelessence', oldPrice: '1400 MAD', newPrice: '1300 MAD', type: 'women', description: '' },
      { name: 'JPG Belle', folder: 'jpgbelle', oldPrice: '1500 MAD', newPrice: '1400 MAD', type: 'women', description: '' },
      { name: 'Scandal Gold EDP', folder: 'scandalgoldedp', oldPrice: '1600 MAD', newPrice: '1500 MAD', type: 'women', description: '' },
      { name: 'The One', folder: 'theone', oldPrice: '1700 MAD', newPrice: '1600 MAD', type: 'women', description: '' }
    ];

    function getImagePath(product) {
      // Always use 1.jpg for gallery
      const folder = product.type === 'men' ? 'img' : 'imgg';
      return `${folder}/${escapeHTML(product.folder)}/1.jpg`;
    }

    function renderGallery(products, type, containerId) {
      const container = document.getElementById(containerId);
      container.innerHTML = products
        .filter(p => p.type === type)
        .map(p => `
          <div class="product-card">
            <div class="product-image-wrapper">
              <a href="product.html?folder=${encodeURIComponent(p.folder)}">
                <img src="${getImagePath(p)}" alt="${escapeHTML(p.name)}" class="product-image" loading="lazy"
                  onerror="this.onerror=null;this.src='https://via.placeholder.com/160x200?text=No+Image';" />
              </a>
            </div>
            <div class="product-title">${escapeHTML(p.name)}</div>
            <div class="product-type">${p.type === 'men' ? 'Homme' : 'Femme'}</div>
            <div class="product-prices">
              ${p.oldPrice ? `<span class="old-price">${escapeHTML(p.oldPrice)}</span>` : ''}
              <span class="new-price">${escapeHTML(p.newPrice)}</span>
            </div>
            <a class="product-link" href="product.html?folder=${encodeURIComponent(p.folder)}">Voir le parfum</a>
          </div>
        `).join('');
    }

    renderGallery(products, 'men', 'gallery-men');
    renderGallery(products, 'women', 'gallery-women');
  </script>


</body></html>